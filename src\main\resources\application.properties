spring.application.name=test_spring
server.port=8086

# MySQL Database Configuration
spring.datasource.url=**********************************************************************************************************************************
spring.datasource.username=omar
spring.datasource.password=HALAMADRID15=16
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true