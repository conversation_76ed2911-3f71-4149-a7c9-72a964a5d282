# Simple MySQL Setup for test_spring

## Prerequisites
- MySQL Server running on `localhost:3306`
- Root user accessible (or change credentials in application.properties)

## Configuration
The database `test_spring` will be **automatically created** when you start the application.

## What's Included
1. **Database Configuration** (`application.properties`) - MySQL connection settings
2. **Test Entity** (`TestEntity.java`) - Simple entity for testing
3. **Repository** (`TestEntityRepository.java`) - JPA repository for CRUD operations  
4. **REST Controller** (`TestController.java`) - API endpoints to test database

## API Endpoints
- `GET /api/test/health` - Database health check
- `GET /api/test` - Get all test entities
- `POST /api/test` - Create a new test entity
- `GET /api/test/{id}` - Get test entity by ID

## Running the Application
1. Make sure MySQL is running
2. Run: `./mvnw spring-boot:run`
3. Test: `http://localhost:8084/api/test/health`

## Customizing Database Settings
Edit `src/main/resources/application.properties`:
```properties
spring.datasource.username=your_username
spring.datasource.password=your_password
```
