package com.example.test_spring;

import com.example.test_spring.entity.TestEntity;
import com.example.test_spring.repository.TestEntityRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;
import java.sql.Connection;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify database connectivity and basic operations
 */
@SpringBootTest
@TestPropertySource(properties = {
        "spring.datasource.url=jdbc:h2:mem:testdb",
        "spring.datasource.driver-class-name=org.h2.Driver",
        "spring.jpa.hibernate.ddl-auto=create-drop",
        "spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect"
})
class DatabaseConnectionTest {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private TestEntityRepository testEntityRepository;

    @Test
    void testDatabaseConnection() throws Exception {
        // Test that we can get a connection
        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection);
            assertFalse(connection.isClosed());
        }
    }

    @Test
    void testEntityOperations() {
        // Test creating an entity
        TestEntity entity = new TestEntity();
        entity.setName("Test Entity");
        entity.setDescription("Test Description");

        TestEntity saved = testEntityRepository.save(entity);
        assertNotNull(saved.getId());
        assertEquals("Test Entity", saved.getName());
        assertNotNull(saved.getCreatedAt());

        // Test finding the entity
        TestEntity found = testEntityRepository.findById(saved.getId()).orElse(null);
        assertNotNull(found);
        assertEquals("Test Entity", found.getName());

        // Test search functionality
        var searchResults = testEntityRepository.findByNameContainingIgnoreCase("test");
        assertFalse(searchResults.isEmpty());
        assertTrue(searchResults.stream().anyMatch(e -> e.getName().equals("Test Entity")));
    }
}
